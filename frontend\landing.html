<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaGard</title>
    <link rel="icon" type="image/png" href="/favicon.png">
    <link rel="stylesheet" href="/style.css">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.2.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/locomotive-scroll@3.5.4/dist/locomotive-scroll.css">
</head>
<body>
    <div id="main">
        <div data-scroll data-scroll-speed="-5" id="page1">
            <nav>
                <img src="data:image/png;base64,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" alt="">
                <div id="right-nav">
                    <a href="/index.html" style="padding: 10px 20px; border-radius: 50px; background-color: #0b48ed; border: 1px solid #fff; color: #fff; font-size: 15px; cursor: pointer; transition: all 0.3s ease; z-index: 99999; position: relative; text-decoration: none; display: inline-block;" onmouseover="this.style.backgroundColor='#fff'; this.style.color='#0b48ed';" onmouseout="this.style.backgroundColor='#0b48ed'; this.style.color='#fff';">Get Started</a>
                    <button><i class="ri-menu-fill"></i></button>
                </div>
            </nav>
            <div class="bottom-page1">
                <h1>Experience Real  <br> vulnerability Audit</h1>
                <div class="bottom-page1-inner">
                    <h4>Deploy Smarter, Not Slower.
<br> Real-Time, AI-Powered Smart Contract Auditing.</h4>
                    <a href="http://localhost:5176/app" target="_blank" style="padding: 10px 20px; border: none; border-radius: 50px; background-color: #fff; color: #0b48ed; font-size: 16px; cursor: pointer; text-decoration: none; display: inline-block;">LEARN MORE</a>
                </div>
            </div>
            <video src="https://thisismagma.com/wp-content/themes/magma/assets/home/<USER>/1.mp4?2" autoplay loop muted ></video>
        </div>
        <div id="page2">
            <h2>AI Meets Audit</h2>
            <h1>Our platform uses multi-agent AI models, blockchain forensics, and economic simulators to audit smart contracts in real-time — without slowing down your development or interfering with your UI.
            </h1>
        </div>
        <div id="page3">
            <canvas></canvas>
        </div>
        <div id="page4">
            <h3> Fast. Precise. Developer-First.</h3>
            <h1>With a backend-only API-first architecture, NovaGuard plugs directly into your stack. No UI changes. No learning curve. Just instant audit intelligence at your fingertips.

</h1>
        </div>
        <div id="page5">
            <canvas></canvas>
        </div>
        <div id="page6">
            <h3>Multi-Chain Ready</h3>
            <h1>From Ethereum to zkSync, NovaGuard supports cross-chain analysis out of the box. Whether you're deploying on Arbitrum, Optimism, or Base, you're covered.

</h1>
        </div>
        <div id="page7">
            <div class="page7-cir">
                <div class="page7-cir-inner"></div>
            </div>
            <canvas></canvas>
        </div>
        <div id="page8">
            <div class="page8-bottom">
                <h1>NovaGard</h1>
                <a href="http://localhost:5176/app" target="_blank" style="padding: 10px 20px; border-radius: 50px; background-color: #0b48ed; border: 1px solid #fff; color: #fff; font-size: 15px; cursor: pointer; transition: all 0.3s ease; z-index: 99999; position: relative; text-decoration: none; display: inline-block;" onmouseover="this.style.backgroundColor='#fff'; this.style.color='#0b48ed';" onmouseout="this.style.backgroundColor='#0b48ed'; this.style.color='#fff';">Payment Demo</a>
            </div>
            <video src="https://thisismagma.com/wp-content/themes/magma/assets/home/<USER>/1.webm?2" autoplay loop muted></video>
        </div>
        <div id="page9">
            <div class="left9">
                <h1>What is <br> NovaGard?</h1>
            </div>
            <div class="right9">
                <div class="right9-center"></div>
            </div>
        </div>
        <div id="page10">
            <div class="right10">
                <div class="right10-inner">
                    <h1>Web3 platform
                    </h1>
                    <p>NovaGuard is an AI-first backend security engine for smart contracts.
It analyzes Solidity code, bytecode, or live deployments using LLMs to detect vulnerabilities, economic risks, and optimization flaws — all in real time.</p>
                </div>
                <div class="right10-inner">
                    <h1>AI-Powered Smart Contract Security</h1>
                    <p>In a space where billions of dollars are locked in code, smart contract security isn’t optional — it’s critical. NovaGuard was created to make next-generation security accessible, intelligent, and instant.</p>
                </div>
                <div class="right10-inner">
                    <h1>Built for Web3 Builders</h1>
                    <p>Whether you're building a DAO, launching a DeFi protocol, or deploying governance logic on Ethereum or Layer 2, NovaGuard gives you automated, API-first security coverage backed by cutting-edge AI models like google/gemma-3n-e4b-it.

</p>
                </div>
            </div>
        </div>
        <div id="page11">
            <h1>Recent Blog</h1>
            <div class="page11-inner">
                <div class="left11">
                    <img src="https://thisismagma.com/wp-content/uploads/2023/05/thisismagma.com-magma-a-la-3eme-edition-du-tech-talk-de-cbre-france-1684942208038.jpeg" alt="">
                </div>
                <div class="right11">
                    <h4>MAY 30,2025</h4>
                    <h1>NovaGuard At CBRE France's 3rd Tech Talk</h1>

                </div>
            </div>
            <div class="page11-inner">
                <div class="left11">
                    <img src="https://thisismagma.com/wp-content/uploads/2023/05/thisismagma.com-workshop-blockchain-et-actif-digitaux-sba-16-min-scaled.jpg" alt="">
                </div>
                <div class="right11">
                    <h4>MAY 30,2025</h4>
                    <h1>NovaGuard At CBRE France's 3rd Tech Talk</h1>

                </div>
            </div>
            <div class="page11-inner">
                <div class="left11">
                    <img src="https://thisismagma.com/wp-content/uploads/2023/03/magma.wp2.cubdev.com-exploring-the-potential-of-real-estate-tokenization-digital-twins-magma.png" alt="">
                </div>
                <div class="right11">
                    <h4>MAY 30,2025</h4>
                    <h1>NovaGuard At CBRE France's 3rd Tech Talk</h1>

                </div>
            </div>
          
        </div>
        <div id="page12">
            <div class="page12-inner">
                <h1>Featured In</h1>
                <p>Thrilled to have been featured in several prominent media <br> outlets and leading professionals across the world's best <br> real estate and web3 institutions.</p>
            </div>
        </div>
        <div id="page13">
            <h1>Become an
                <br>
                early adopter</h1>
                <a href="http://localhost:5176/app" target="_blank" style="padding: 18px 30px; border: none; background-color: #0a3cce; color: #fff; border-radius: 50px; font-size: 16px; cursor: pointer; text-decoration: none; display: inline-block;">BOOK A DEMO</a>
        </div>
        <div id="page14">
            <div class="page14-inner">
                <h1>Twitter</h1>
                <i class="ri-arrow-right-up-line"></i>
                <div class="center14"></div>
            </div>
            <div class="page14-inner">
                <h1>LinkedIn</h1>
                <i class="ri-arrow-right-up-line"></i>
                <div class="center14"></div>

            </div>
            <div class="page14-inner">
                <h1>Instagram</h1>
                <i class="ri-arrow-right-up-line"></i>
                <div class="center14"></div>

            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/locomotive-scroll@3.5.4/dist/locomotive-scroll.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.1/gsap.min.js" integrity="sha512-qF6akR/fsZAB4Co1QDDnUXWnaQseLGXoniuSuSlPQK6+aWhlMZcHzkasCSlnWoe+TJuudlka1/IQ01Dnhgq95g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.1/ScrollTrigger.min.js" integrity="sha512-IHDCHrefnBT3vOCsvdkMvJF/MCPz/nBauQLzJkupa4Gn4tYg5a6VGyzIrjo6QAUy3We5HFOZUlkUpP0dkgE60A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script>
        // Function to open React App
        function openReactApp() {
            console.log('Get Started button clicked on landing page');
            console.log('Current location:', window.location.href);
            console.log('Navigating to React app dashboard...');

            // Navigate directly to the React app running on Vite dev server
            try {
                // Navigate to the vulnerability dashboard page
                window.location.href = 'http://localhost:5176/app';
            } catch (error) {
                console.error('Navigation error:', error);
                // Fallback: navigate to React app home
                window.location.href = 'http://localhost:5176/';
            }
        }
        
        // Test function on page load
        window.addEventListener('load', function() {
            console.log('Landing page loaded successfully');
            console.log('Current URL:', window.location.href);
        });
    </script>
    <script src="/script.js"></script>
</body>
</html>
