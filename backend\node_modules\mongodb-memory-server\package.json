{"name": "mongodb-memory-server", "version": "10.1.4", "description": "MongoDB Server for testing (auto-download latest version). The server will allow you to connect your favourite ODM or client library to the MongoDB Server and run parallel integration tests isolated from each other.", "main": "index.js", "types": "index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/typegoose/mongodb-memory-server.git", "directory": "packages/mongodb-memory-server"}, "engines": {"node": ">=16.20.1"}, "homepage": "https://github.com/typegoose/mongodb-memory-server", "keywords": ["mongodb", "mongoose", "mock", "stub", "mockgoose", "mongodb-prebuilt", "mongomem"], "dependencies": {"mongodb-memory-server-core": "10.1.4", "tslib": "^2.7.0"}, "scripts": {"postinstall": "node ./postinstall.js"}}