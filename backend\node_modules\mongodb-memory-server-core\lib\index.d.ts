import './util/resolveConfig';
import { MongoMemoryServer } from './MongoMemoryServer';
export { DryMongoBinary } from './util/DryMongoBinary';
export { MongoBinary } from './util/MongoBinary';
export { MongoInstance } from './util/MongoInstance';
export { MongoMemoryReplSet } from './MongoMemoryReplSet';
export * as errors from './util/errors';
export { MongoMemoryServer };
export default MongoMemoryServer;
//# sourceMappingURL=index.d.ts.map