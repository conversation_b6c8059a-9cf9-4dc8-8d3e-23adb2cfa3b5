# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [0.2.1](https://github.com/cfware/node-preload/compare/v0.2.0...v0.2.1) (2019-12-22)


### Bug Fixes

* Support jumping between node.js versions ([#7](https://github.com/cfware/node-preload/issues/7)) ([68950a0](https://github.com/cfware/node-preload/commit/68950a07ab153cda5e0b5fec1407973169443bbb)), closes [istanbuljs/nyc#1246](https://github.com/istanbuljs/nyc/issues/1246)

## [0.2.0](https://github.com/cfware/node-preload/compare/v0.1.4...v0.2.0) (2019-12-16)


### ⚠ BREAKING CHANGES

* The API is completely rewritten, the export is now an
Array.
* Propagated variables are no longer supported.  For this
functionality preload a local module which uses `process-on-spawn`
directly.

### Features

* Use process-on-spawn ([#6](https://github.com/cfware/node-preload/issues/6)) ([2decb3a](https://github.com/cfware/node-preload/commit/2decb3a32a00084d089d964de6440e9f7817d563))

## [0.1.4](https://github.com/cfware/node-preload/compare/v0.1.3...v0.1.4) (2019-11-01)


### Features

* Support running under yarn pnp ([#3](https://github.com/cfware/node-preload/issues/3)) ([15d949b](https://github.com/cfware/node-preload/commit/15d949b)), closes [istanbuljs/nyc#1204](https://github.com/istanbuljs/nyc/issues/1204)

## [0.1.3](https://github.com/cfware/node-preload/compare/v0.1.2...v0.1.3) (2019-10-07)


### Bug Fixes

* Properly handle backslashes in paths on Node.js 12 ([#2](https://github.com/cfware/node-preload/issues/2)) ([2ad4448](https://github.com/cfware/node-preload/commit/2ad4448))

## [0.1.2](https://github.com/cfware/node-preload/compare/v0.1.1...v0.1.2) (2019-09-24)


### Bug Fixes

* esm must be loaded from the internal/preload module ([a5444dd](https://github.com/cfware/node-preload/commit/a5444dd))

## [0.1.1](https://github.com/cfware/node-preload/compare/v0.1.0...v0.1.1) (2019-09-23)


### Bug Fixes

* Add `main` to package.json ([6b39401](https://github.com/cfware/node-preload/commit/6b39401))
* Handle situations where a preload might require node-preload. ([3b41164](https://github.com/cfware/node-preload/commit/3b41164))

## 0.1.0 (2019-09-23)


### Features

* Initial implementation ([0818e8c](https://github.com/cfware/node-preload/commit/0818e8c))
